const fs = require('fs');
const readline = require('readline');
const path = require('path');

const INPUT_CSV = 'users_202506011046.csv';
const OUTPUT_DIR = 'output_chunks';
const MAX_FILE_SIZE_MB = 50;

if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR);
}

async function splitCsvFile() {
  const rl = readline.createInterface({
    input: fs.createReadStream(INPUT_CSV)
  });

  let fileIndex = 1;
  let currentFileSize = 0;
  let header = null;
  let output = fs.createWriteStream(path.join(OUTPUT_DIR, `chunk_${fileIndex}.csv`));
  
  for await (const line of rl) {
    if (!header) {
      header = line;
      output.write(header + '\n');
      continue;
    }

    const lineSize = Buffer.byteLength(line + '\n');
    if (currentFileSize + lineSize >= MAX_FILE_SIZE_MB * 1024 * 1024) {
      output.end();  // Close current file
      fileIndex++;
      currentFileSize = 0;
      output = fs.createWriteStream(path.join(OUTPUT_DIR, `chunk_${fileIndex}.csv`));
      output.write(header + '\n'); // Write header to new file
    }

    output.write(line + '\n');
    currentFileSize += lineSize;
  }

  output.end();
  console.log(`CSV split into ${fileIndex} file(s) of approx 50MB each.`);
}

splitCsvFile().catch(console.error);