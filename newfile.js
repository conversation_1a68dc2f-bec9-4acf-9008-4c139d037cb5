import dotenv from 'dotenv';
import tunnel from 'tunnel-ssh';
import fs from 'fs';
import { Pool } from 'pg';

dotenv.config();

const tunnelConfig = {
  username: process.env.SSH_USER,
  host: process.env.SSH_HOST,
  port: 22,
  privateKey: fs.readFileSync(process.env.SSH_KEY_PATH),
  dstHost: process.env.DB_HOST,
  dstPort: parseInt(process.env.DB_PORT),
  localHost: '127.0.0.1',
  localPort: 5433,
};

tunnel(tunnelConfig, (error, server) => {
  if (error) {
    console.error('SSH tunnel error:', error);
    return;
  }
  console.log('✅ SSH tunnel established.');

  const pool = new Pool({
    user: process.env.DB_USER,
    host: '127.0.0.1',  // connect to local port where tunnel listens
    database: process.env.DB_NAME,
    password: process.env.DB_PASS,
    port: 5433,          // must match tunnelConfig.localPort
  });

  pool.query('SELECT NOW()', (err, res) => {
    if (err) {
      console.error('DB query error:', err);
    } else {
      console.log('DB time:', res.rows[0]);
    }
    pool.end(() => {
      server.close();
      console.log('Tunnel and DB pool closed');
    });
  });
});