import dotenv from 'dotenv';
import pkg from 'pg';
import { createSSHTunnel, closeSSHTunnel } from './index.js';

const { Pool } = pkg;

// Load environment variables
dotenv.config();

/**
 * <PERSON>ript to fetch and display a list of users from the database
 */

// Global variables
let dbPool = null;
let sshTunnelActive = false;

// Function to create database pool
function createDatabasePool(useTunnel = false) {
  const config = {
    host: useTunnel ? '127.0.0.1' : process.env.DBHOSt,
    user: process.env.DBUSER,
    password: process.env.DBPASS,
    database: process.env.DBNAME,
    port: useTunnel ? parseInt(process.env.SSH_LOCAL_PORT) || 5433 : parseInt(process.env.DATABASEPORT) || 5432,
    ssl: useTunnel ? false : {
      rejectUnauthorized: false
    },
    max: 10,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 5000,
  };

  return new Pool(config);
}

// Function to execute queries with the current pool
async function executeQuery(text, params = []) {
  if (!dbPool) {
    throw new Error('Database pool not initialized');
  }

  const client = await dbPool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } catch (err) {
    console.error('Query execution error:', err);
    throw err;
  } finally {
    client.release();
  }
}

// Function to test database connection
async function testConnection() {
  try {
    const client = await dbPool.connect();
    console.log('✅ Database connected successfully!');

    const result = await client.query('SELECT NOW() as current_time');
    console.log('📅 Current database time:', result.rows[0].current_time);

    client.release();
    return true;
  } catch (err) {
    console.error('❌ Database connection failed:', err.message);
    return false;
  }
}

// Function to close database pool
async function closePool() {
  if (dbPool) {
    try {
      await dbPool.end();
      console.log('🔌 Database pool closed');
    } catch (err) {
      console.error('Error closing database pool:', err);
    }
    dbPool = null;
  }

  if (sshTunnelActive) {
    await closeSSHTunnel();
    sshTunnelActive = false;
  }
}

// Function to format user data for display
function formatUser(user, index) {
  const createdDate = new Date(user.createdat || user.created_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  const updatedDate = new Date(user.modifiedat || user.updated_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  return {
    '#': index + 1,
    'ID': user.id,
    'User ID': user.user_id || 'N/A',
    'Name': user.full_name || user.name || 'N/A',
    'Email': user.email_id || user.email || 'N/A',
    'Phone': user.phone_number || 'N/A',
    'Type': user.user_type || 'N/A',
    'Created': createdDate,
    'Modified': updatedDate
  };
}

// Function to display users in a formatted table
function displayUsersTable(users) {
  if (users.length === 0) {
    console.log('📭 No users found in the database.');
    return;
  }

  console.log('\n📋 Users List:');
  console.log('═'.repeat(100));
  
  // Format users for display
  const formattedUsers = users.map((user, index) => formatUser(user, index));
  
  // Display table header
  console.log(
    '│ ' + 
    '#'.padEnd(3) + ' │ ' +
    'ID'.padEnd(6) + ' │ ' +
    'Email'.padEnd(35) + ' │ ' +
    'Created'.padEnd(18) + ' │ ' +
    'Updated'.padEnd(18) + ' │'
  );
  console.log('├' + '─'.repeat(98) + '┤');
  
  // Display each user
  formattedUsers.forEach(user => {
    console.log(
      '│ ' + 
      String(user['#']).padEnd(3) + ' │ ' +
      String(user['ID']).padEnd(6) + ' │ ' +
      user['Email'].padEnd(35) + ' │ ' +
      user['Created'].padEnd(18) + ' │ ' +
      user['Updated'].padEnd(18) + ' │'
    );
  });
  
  console.log('└' + '─'.repeat(98) + '┘');
  console.log(`\n📊 Total users displayed: ${users.length}`);
}

// Function to get user statistics
async function getUserStats() {
  try {
    const totalUsersResult = await executeQuery('SELECT COUNT(*) as count FROM users');
    const recentUsersResult = await executeQuery(`
      SELECT COUNT(*) as count FROM users 
      WHERE created_at >= NOW() - INTERVAL '30 days'
    `);
    
    return {
      total: parseInt(totalUsersResult.rows[0].count),
      recent: parseInt(recentUsersResult.rows[0].count)
    };
  } catch (err) {
    console.error('Error getting user statistics:', err);
    return { total: 0, recent: 0 };
  }
}

// Function to check if users table exists and has data
async function checkUsersTable() {
  try {
    // Check if users table exists
    const tableExistsResult = await executeQuery(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `);
    
    const tableExists = tableExistsResult.rows[0].exists;
    
    if (!tableExists) {
      console.log('⚠️  Users table does not exist in the database.');
      console.log('💡 You may need to run migrations or create the table first.');
      return false;
    }
    
    // Check table structure
    const columnsResult = await executeQuery(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position;
    `);
    
    console.log('\n🏗️  Users table structure:');
    columnsResult.rows.forEach(col => {
      console.log(`   • ${col.column_name} (${col.data_type}) ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    return true;
  } catch (err) {
    console.error('Error checking users table:', err);
    return false;
  }
}

// Main function to fetch and display users
async function listUsers() {
  console.log('🚀 Starting user list query...');

  try {
    // First try to establish SSH tunnel
    console.log('🔐 Attempting to establish SSH tunnel...');
    try {
      await createSSHTunnel();
      console.log('✅ SSH tunnel established successfully!');
      dbPool = createDatabasePool(true); // Use tunnel
      sshTunnelActive = true;
    } catch (sshError) {
      console.log('⚠️  SSH tunnel failed, using direct connection...');
      console.log('SSH Error:', sshError.message);
      dbPool = createDatabasePool(false); // Direct connection
      sshTunnelActive = false;
    }

    // Test database connection
    console.log('🔄 Testing database connection...');
    const isConnected = await testConnection();

    if (!isConnected) {
      console.log('❌ Cannot connect to database. Please check your connection.');
      return;
    }
    
    // Check if users table exists
    const tableExists = await checkUsersTable();
    if (!tableExists) {
      return;
    }
    
    // Get user statistics
    console.log('\n📊 Getting user statistics...');
    const stats = await getUserStats();
    console.log(`📈 Total users in database: ${stats.total}`);
    console.log(`🆕 Users created in last 30 days: ${stats.recent}`);
    
    if (stats.total === 0) {
      console.log('\n📭 No users found in the database.');
      console.log('💡 You may want to add some test users first.');
      return;
    }
    
    // Fetch 10 users from the database
    console.log('\n🔍 Fetching 10 users from the database...');
    const query = `
      SELECT id, email, created_at, updated_at 
      FROM users 
      ORDER BY created_at DESC 
      LIMIT 10
    `;
    
    const result = await executeQuery(query);
    const users = result.rows;
    
    // Display the users
    displayUsersTable(users);
    
    // Additional information
    if (stats.total > 10) {
      console.log(`\n💡 Showing 10 most recent users out of ${stats.total} total users.`);
    }
    
  } catch (error) {
    console.error('❌ Error fetching users:', error.message);
    console.error('🔍 Full error details:', error);
  } finally {
    // Always close the database connection
    console.log('\n🔌 Closing database connection...');
    await closePool();
  }
}

// Alternative function to get users with pagination
async function listUsersWithPagination(page = 1, limit = 10) {
  const offset = (page - 1) * limit;
  
  try {
    const query = `
      SELECT id, email, created_at, updated_at 
      FROM users 
      ORDER BY created_at DESC 
      LIMIT $1 OFFSET $2
    `;
    
    const result = await executeQuery(query, [limit, offset]);
    return result.rows;
  } catch (error) {
    console.error('Error fetching users with pagination:', error);
    throw error;
  }
}

// Export functions for use in other files
export {
  listUsers,
  listUsersWithPagination,
  formatUser,
  displayUsersTable,
  getUserStats,
  checkUsersTable
};

// Run the main function if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  listUsers().catch(console.error);
}
