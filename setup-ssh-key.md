# SSH Key Setup Instructions

## Step 1: Copy SSH Key File

You need to copy your SSH private key file to this project directory.

```bash
# Copy the SSH key file to the project root
cp /path/to/your/prod-bastion-v6-data.pem ./prod-bastion-v6-data.pem
```

## Step 2: Set Proper Permissions

SSH requires strict permissions on private key files:

```bash
# Set read-only permissions for owner only
chmod 600 prod-bastion-v6-data.pem
```

## Step 3: Verify SSH Connection

Test your SSH connection to the bastion host:

```bash
# Test SSH connection
ssh -i prod-bastion-v6-data.pem ec2-user@*************
```

## Step 4: Test Database Connection

Once the SSH key is set up, test the database connection:

```bash
# Run the application to test SSH tunnel and database connection
node index.js
```

## Expected Output

When successful, you should see:
```
🚀 Starting application...
🔐 Creating SSH tunnel...
📡 Connecting to *************:22 as ec2-user
🎯 Forwarding localhost:5433 -> prod-core-db-cluster.cluster-cbny20waqkln.ap-south-1.rds.amazonaws.com:5432
✅ SSH tunnel established successfully!
🔗 Local port 5433 is now forwarded to prod-core-db-cluster.cluster-cbny20waqkln.ap-south-1.rds.amazonaws.com:5432
✅ Database connected successfully through SSH tunnel!
📅 Current database time: [timestamp]
✨ Application initialized successfully!
```

## Troubleshooting

### SSH Key Permission Issues
```bash
# If you get permission errors:
chmod 600 prod-bastion-v6-data.pem
```

### SSH Connection Issues
```bash
# Test SSH connection manually:
ssh -i prod-bastion-v6-data.pem ec2-user@************* -v
```

### Port Already in Use
```bash
# If port 5433 is already in use, change SSH_LOCAL_PORT in .env:
SSH_LOCAL_PORT="5434"
```

### Network Issues
- Ensure you have internet connectivity
- Check if your firewall allows outbound SSH connections (port 22)
- Verify the bastion host IP is correct and accessible

## Security Notes

- Never commit the SSH private key file to version control
- Keep the key file permissions at 600 (read-only for owner)
- The SSH tunnel encrypts all database traffic
- The application will fall back to direct connection if SSH tunnel fails
