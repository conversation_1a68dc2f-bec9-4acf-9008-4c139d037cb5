import { pool, executeQuery, getConnection } from './index.js';

// Example database operations for a to-do application

/**
 * Create tables if they don't exist
 */
async function createTables() {
  const createUsersTable = `
    CREATE TABLE IF NOT EXISTS users (
      id SERIAL PRIMARY KEY,
      email VARCHAR(255) UNIQUE NOT NULL,
      password_hash VARCHAR(255) NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `;

  const createTodosTable = `
    CREATE TABLE IF NOT EXISTS todos (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      completed BOOLEAN DEFAULT FALSE,
      priority VARCHAR(20) DEFAULT 'medium',
      due_date TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `;

  try {
    await executeQuery(createUsersTable);
    await executeQuery(createTodosTable);
    console.log('✅ Tables created successfully');
  } catch (err) {
    console.error('❌ Error creating tables:', err);
    throw err;
  }
}

/**
 * User operations
 */
const userOperations = {
  // Create a new user
  async create(email, passwordHash) {
    const query = 'INSERT INTO users (email, password_hash) VALUES ($1, $2) RETURNING *';
    const result = await executeQuery(query, [email, passwordHash]);
    return result.rows[0];
  },

  // Find user by email
  async findByEmail(email) {
    const query = 'SELECT * FROM users WHERE email = $1';
    const result = await executeQuery(query, [email]);
    return result.rows[0];
  },

  // Find user by ID
  async findById(id) {
    const query = 'SELECT * FROM users WHERE id = $1';
    const result = await executeQuery(query, [id]);
    return result.rows[0];
  },

  // Update user
  async update(id, updates) {
    const setClause = Object.keys(updates)
      .map((key, index) => `${key} = $${index + 2}`)
      .join(', ');
    
    const query = `UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *`;
    const values = [id, ...Object.values(updates)];
    
    const result = await executeQuery(query, values);
    return result.rows[0];
  },

  // Delete user
  async delete(id) {
    const query = 'DELETE FROM users WHERE id = $1 RETURNING *';
    const result = await executeQuery(query, [id]);
    return result.rows[0];
  }
};

/**
 * Todo operations
 */
const todoOperations = {
  // Create a new todo
  async create(userId, title, description = null, priority = 'medium', dueDate = null) {
    const query = `
      INSERT INTO todos (user_id, title, description, priority, due_date) 
      VALUES ($1, $2, $3, $4, $5) 
      RETURNING *
    `;
    const result = await executeQuery(query, [userId, title, description, priority, dueDate]);
    return result.rows[0];
  },

  // Get all todos for a user
  async getByUserId(userId, completed = null) {
    let query = 'SELECT * FROM todos WHERE user_id = $1';
    const params = [userId];
    
    if (completed !== null) {
      query += ' AND completed = $2';
      params.push(completed);
    }
    
    query += ' ORDER BY created_at DESC';
    
    const result = await executeQuery(query, params);
    return result.rows;
  },

  // Get todo by ID
  async getById(id) {
    const query = 'SELECT * FROM todos WHERE id = $1';
    const result = await executeQuery(query, [id]);
    return result.rows[0];
  },

  // Update todo
  async update(id, updates) {
    const setClause = Object.keys(updates)
      .map((key, index) => `${key} = $${index + 2}`)
      .join(', ');
    
    const query = `UPDATE todos SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *`;
    const values = [id, ...Object.values(updates)];
    
    const result = await executeQuery(query, values);
    return result.rows[0];
  },

  // Mark todo as completed
  async markCompleted(id) {
    return await this.update(id, { completed: true });
  },

  // Mark todo as incomplete
  async markIncomplete(id) {
    return await this.update(id, { completed: false });
  },

  // Delete todo
  async delete(id) {
    const query = 'DELETE FROM todos WHERE id = $1 RETURNING *';
    const result = await executeQuery(query, [id]);
    return result.rows[0];
  },

  // Get todos by priority
  async getByPriority(userId, priority) {
    const query = 'SELECT * FROM todos WHERE user_id = $1 AND priority = $2 ORDER BY created_at DESC';
    const result = await executeQuery(query, [userId, priority]);
    return result.rows;
  },

  // Get overdue todos
  async getOverdue(userId) {
    const query = `
      SELECT * FROM todos 
      WHERE user_id = $1 AND due_date < CURRENT_TIMESTAMP AND completed = FALSE 
      ORDER BY due_date ASC
    `;
    const result = await executeQuery(query, [userId]);
    return result.rows;
  }
};

/**
 * Database statistics
 */
async function getStats() {
  try {
    const userCountResult = await executeQuery('SELECT COUNT(*) as count FROM users');
    const todoCountResult = await executeQuery('SELECT COUNT(*) as count FROM todos');
    const completedTodosResult = await executeQuery('SELECT COUNT(*) as count FROM todos WHERE completed = TRUE');
    
    return {
      totalUsers: parseInt(userCountResult.rows[0].count),
      totalTodos: parseInt(todoCountResult.rows[0].count),
      completedTodos: parseInt(completedTodosResult.rows[0].count),
      pendingTodos: parseInt(todoCountResult.rows[0].count) - parseInt(completedTodosResult.rows[0].count)
    };
  } catch (err) {
    console.error('Error getting stats:', err);
    throw err;
  }
}

export {
  createTables,
  userOperations,
  todoOperations,
  getStats
};
