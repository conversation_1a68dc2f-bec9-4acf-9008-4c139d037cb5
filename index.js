import dotenv from 'dotenv';
import pkg from 'pg';
const { Pool } = pkg;

// Load environment variables from .env file
dotenv.config();

// Database configuration using environment variables
const dbConfig = {
  host: process.env.DBHOSt,
  user: process.env.DBUSER,
  password: process.env.DBPASS,
  database: process.env.DBNAME,
  port: parseInt(process.env.DATABASEPORT) || 5432,
  ssl: {
    rejectUnauthorized: false // For AWS RDS connections
  },
  // Connection pool settings
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle
  connectionTimeoutMillis: 2000, // How long to wait when connecting a new client
};

// Create a connection pool
const pool = new Pool(dbConfig);

// Handle pool errors
pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// Function to test database connection
async function testConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ Database connected successfully!');
    
    // Test query to verify connection
    const result = await client.query('SELECT NOW() as current_time');
    console.log('📅 Current database time:', result.rows[0].current_time);
    
    client.release();
    return true;
  } catch (err) {
    console.error('❌ Database connection failed:', err.message);
    return false;
  }
}

// Function to execute queries
async function executeQuery(text, params = []) {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } catch (err) {
    console.error('Query execution error:', err);
    throw err;
  } finally {
    client.release();
  }
}

// Function to get database connection from pool
async function getConnection() {
  try {
    return await pool.connect();
  } catch (err) {
    console.error('Failed to get database connection:', err);
    throw err;
  }
}

// Graceful shutdown function
async function closePool() {
  try {
    await pool.end();
    console.log('🔌 Database pool closed');
  } catch (err) {
    console.error('Error closing database pool:', err);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, closing database connections...');
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, closing database connections...');
  await closePool();
  process.exit(0);
});

// Export the database utilities
export {
  pool,
  testConnection,
  executeQuery,
  getConnection,
  closePool
};

// Main function to initialize the application
async function main() {
  console.log('🚀 Starting application...');
  
  // Test database connection on startup
  const isConnected = await testConnection();
  
  if (isConnected) {
    console.log('✨ Application initialized successfully!');
    
    // Example usage - uncomment to test
    // try {
    //   const result = await executeQuery('SELECT version()');
    //   console.log('📊 Database version:', result.rows[0].version);
    // } catch (err) {
    //   console.error('Error executing test query:', err);
    // }
  } else {
    console.log('❌ Application failed to initialize due to database connection issues');
    process.exit(1);
  }
}

// Run the main function if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
