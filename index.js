import dotenv from 'dotenv';
import pkg from 'pg';
import { Client } from 'ssh2';
import net from 'net';
import fs from 'fs';
import path from 'path';
const { Pool } = pkg;

// Load environment variables from .env file
dotenv.config();

// SSH Tunnel configuration
let sshConfig = null;

// Check if SSH key file exists before creating config
const sshKeyPath = process.env.SSH_KEY_PATH;
if (sshKeyPath && fs.existsSync(path.resolve(sshKeyPath))) {
  try {
    sshConfig = {
      host: process.env.SSH_HOST,
      username: process.env.SSH_USER,
      privateKey: fs.readFileSync(path.resolve(sshKeyPath)),
      port: 22,
      dstHost: process.env.DBHOSt,
      dstPort: parseInt(process.env.SSH_REMOTE_PORT) || 5432,
      localHost: '127.0.0.1',
      localPort: parseInt(process.env.SSH_LOCAL_PORT) || 5433,
      keepAlive: true
    };
    console.log('🔑 SSH configuration loaded successfully');
  } catch (error) {
    console.warn('⚠️  Failed to load SSH key:', error.message);
    sshConfig = null;
  }
} else {
  console.warn('⚠️  SSH key file not found at:', sshKeyPath);
  console.warn('📝 Please follow setup-ssh-key.md instructions to configure SSH access');
}

// Global variable to store tunnel connection
let sshTunnel = null;

// Database configuration using environment variables
// When using SSH tunnel, connect to localhost on the tunnel port
const dbConfig = {
  host: sshTunnel ? '127.0.0.1' : process.env.DBHOSt,
  user: process.env.DBUSER,
  password: process.env.DBPASS,
  database: process.env.DBNAME,
  port: sshTunnel ? parseInt(process.env.SSH_LOCAL_PORT) : parseInt(process.env.DATABASEPORT) || 5432,
  ssl: sshTunnel ? false : {
    rejectUnauthorized: false // For AWS RDS connections
  },
  // Connection pool settings
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle
  connectionTimeoutMillis: 5000, // Increased timeout for SSH tunnel
};

// Create a connection pool
const pool = new Pool(dbConfig);

// Handle pool errors
pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// Function to create SSH tunnel
async function createSSHTunnel() {
  if (!sshConfig) {
    throw new Error('SSH configuration not available. Please check your SSH key file.');
  }

  return new Promise((resolve, reject) => {
    console.log('🔐 Creating SSH tunnel...');
    console.log(`📡 Connecting to ${sshConfig.host}:${sshConfig.port} as ${sshConfig.username}`);
    console.log(`🎯 Forwarding localhost:${sshConfig.localPort} -> ${sshConfig.dstHost}:${sshConfig.dstPort}`);

    const sshClient = new Client();

    sshClient.on('ready', () => {
      console.log('✅ SSH connection established!');

      // Create local server to listen for connections
      const server = net.createServer((localSocket) => {
        // Forward connection through SSH tunnel
        sshClient.forwardOut(
          '127.0.0.1', // source IP
          0, // source port (0 = any)
          sshConfig.dstHost, // destination IP
          sshConfig.dstPort, // destination port
          (err, stream) => {
            if (err) {
              console.error('❌ SSH forwarding failed:', err.message);
              localSocket.end();
              return;
            }

            // Pipe data between local socket and SSH stream
            localSocket.pipe(stream).pipe(localSocket);

            stream.on('close', () => {
              localSocket.end();
            });

            localSocket.on('close', () => {
              stream.close();
            });
          }
        );
      });

      server.listen(sshConfig.localPort, '127.0.0.1', () => {
        console.log('✅ SSH tunnel established successfully!');
        console.log(`🔗 Local port ${sshConfig.localPort} is now forwarded to ${sshConfig.dstHost}:${sshConfig.dstPort}`);
        sshTunnel = { server, sshClient };
        resolve({ server, sshClient });
      });

      server.on('error', (err) => {
        console.error('🚨 Local server error:', err.message);
        reject(err);
      });
    });

    sshClient.on('error', (err) => {
      console.error('🚨 SSH connection error:', err.message);
      reject(err);
    });

    // Connect to SSH server
    sshClient.connect({
      host: sshConfig.host,
      port: sshConfig.port,
      username: sshConfig.username,
      privateKey: sshConfig.privateKey,
      keepaliveInterval: 30000,
      keepaliveCountMax: 3
    });
  });
}

// Function to close SSH tunnel
async function closeSSHTunnel() {
  if (sshTunnel) {
    try {
      if (sshTunnel.server) {
        sshTunnel.server.close();
      }
      if (sshTunnel.sshClient) {
        sshTunnel.sshClient.end();
      }
      sshTunnel = null;
      console.log('🔌 SSH tunnel closed');
    } catch (err) {
      console.error('Error closing SSH tunnel:', err);
    }
  }
}

// Function to test database connection
async function testConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ Database connected successfully!');
    
    // Test query to verify connection
    const result = await client.query('SELECT NOW() as current_time');
    console.log('📅 Current database time:', result.rows[0].current_time);
    
    client.release();
    return true;
  } catch (err) {
    console.error('❌ Database connection failed:', err.message);
    return false;
  }
}

// Function to execute queries
async function executeQuery(text, params = []) {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } catch (err) {
    console.error('Query execution error:', err);
    throw err;
  } finally {
    client.release();
  }
}

// Function to get database connection from pool
async function getConnection() {
  try {
    return await pool.connect();
  } catch (err) {
    console.error('Failed to get database connection:', err);
    throw err;
  }
}

// Graceful shutdown function
async function closePool() {
  try {
    if (!poolClosed) {
      await activePool.end();
      poolClosed = true;
      console.log('🔌 Database pool closed');
    }
  } catch (err) {
    console.error('Error closing database pool:', err);
  }

  // Close SSH tunnel if it exists
  await closeSSHTunnel();
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, closing database connections...');
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, closing database connections...');
  await closePool();
  process.exit(0);
});

// Global variable to track active pool
let activePool = pool;
let poolClosed = false;

// Function to recreate pool with tunnel configuration
function recreatePoolWithTunnel() {
  // Update dbConfig for tunnel
  const tunnelDbConfig = {
    host: '127.0.0.1',
    user: process.env.DBUSER,
    password: process.env.DBPASS,
    database: process.env.DBNAME,
    port: parseInt(process.env.SSH_LOCAL_PORT) || 5433,
    ssl: false, // No SSL needed for local tunnel
    // Connection pool settings
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 5000,
  };

  // Close existing pool if not already closed
  if (!poolClosed) {
    pool.end();
    poolClosed = true;
  }

  // Create new pool with tunnel config
  activePool = new Pool(tunnelDbConfig);
  return activePool;
}

// Export the database utilities
export {
  pool,
  testConnection,
  executeQuery,
  getConnection,
  closePool,
  createSSHTunnel,
  closeSSHTunnel
};

// Main function to initialize the application
async function main() {
  console.log('🚀 Starting application...');

  try {
    // First, try to establish SSH tunnel
    await createSSHTunnel();

    // Recreate pool with tunnel configuration
    const tunnelPool = recreatePoolWithTunnel();

    // Test database connection through tunnel
    const client = await tunnelPool.connect();
    console.log('✅ Database connected successfully through SSH tunnel!');

    // Test query to verify connection
    const result = await client.query('SELECT NOW() as current_time');
    console.log('� Current database time:', result.rows[0].current_time);

    client.release();
    console.log('✨ Application initialized successfully!');

  } catch (error) {
    console.error('❌ Application failed to initialize:', error.message);

    // If SSH tunnel fails, try direct connection
    console.log('🔄 Attempting direct database connection...');
    try {
      const isConnected = await testConnection();
      if (isConnected) {
        console.log('✨ Application initialized with direct connection!');
      } else {
        console.log('❌ Both SSH tunnel and direct connection failed');
        process.exit(1);
      }
    } catch (directError) {
      console.error('❌ Direct connection also failed:', directError.message);
      process.exit(1);
    }
  }
}

// Run the main function if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
