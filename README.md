# TestCases

## Database Connection Setup

This project now includes PostgreSQL database connection functionality using the credentials from your `.env` file.

### Files Added:

1. **`index.js`** - Main application entry point with database connection pool
2. **`database.js`** - Database operations and utilities for users and todos
3. **`example-usage.js`** - Example showing how to use the database operations

### Environment Variables

Make sure your `.env` file contains the following variables:
```
DBHOSt="your-database-host"
DBUSER="your-username"
DBPASS="your-password"
DBNAME="your-database-name"
DATABASEPORT="5432"
```

### Dependencies Added:

- `pg` - PostgreSQL client for Node.js
- `dotenv` - Environment variable loader

### Usage:

#### Basic Connection Test:
```bash
node index.js
```

#### Run Example Operations:
```bash
node example-usage.js
```

#### Import in Your Code:
```javascript
import { pool, executeQuery, testConnection } from './index.js';
import { userOperations, todoOperations } from './database.js';

// Test connection
await testConnection();

// Create a user
const user = await userOperations.create('<EMAIL>', 'hashedPassword');

// Create a todo
const todo = await todoOperations.create(user.id, 'My Todo', 'Description');
```

### Database Schema:

The application creates two main tables:

#### Users Table:
- `id` (SERIAL PRIMARY KEY)
- `email` (VARCHAR UNIQUE)
- `password_hash` (VARCHAR)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

#### Todos Table:
- `id` (SERIAL PRIMARY KEY)
- `user_id` (INTEGER, foreign key to users)
- `title` (VARCHAR)
- `description` (TEXT)
- `completed` (BOOLEAN)
- `priority` (VARCHAR)
- `due_date` (TIMESTAMP)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Available Operations:

#### User Operations:
- `userOperations.create(email, passwordHash)`
- `userOperations.findByEmail(email)`
- `userOperations.findById(id)`
- `userOperations.update(id, updates)`
- `userOperations.delete(id)`

#### Todo Operations:
- `todoOperations.create(userId, title, description, priority, dueDate)`
- `todoOperations.getByUserId(userId, completed)`
- `todoOperations.getById(id)`
- `todoOperations.update(id, updates)`
- `todoOperations.markCompleted(id)`
- `todoOperations.markIncomplete(id)`
- `todoOperations.delete(id)`
- `todoOperations.getByPriority(userId, priority)`
- `todoOperations.getOverdue(userId)`

### Connection Pool Features:

- Automatic connection pooling with up to 20 concurrent connections
- SSL support for AWS RDS connections
- Graceful shutdown handling
- Error handling and logging
- Connection timeout management

### Notes:

- The database connection may timeout if you're not connected to the appropriate network/VPN for the production database
- All database operations use parameterized queries to prevent SQL injection
- The connection pool automatically handles connection management
- Tables are created automatically when you run the application