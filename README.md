# TestCases

## Database Connection Setup

This project now includes PostgreSQL database connection functionality using the credentials from your `.env` file.

### Files Added:

1. **`index.js`** - Main application entry point with database connection pool
2. **`database.js`** - Database operations and utilities for users and todos
3. **`example-usage.js`** - Example showing how to use the database operations

### Environment Variables

Make sure your `.env` file contains the following variables:
```
DBHOSt="your-database-host"
DBUSER="your-username"
DBPASS="your-password"
DBNAME="your-database-name"
DATABASEPORT="5432"

# SSH Tunnel Configuration
SSH_HOST="your-bastion-host-ip"
SSH_USER="ec2-user"
SSH_KEY_PATH="./prod-bastion-v6-data.pem"
SSH_LOCAL_PORT="5433"
SSH_REMOTE_PORT="5432"
```

### SSH Key Setup

**Important:** You need to place your SSH private key file in the project root directory:

1. Copy your `prod-bastion-v6-data.pem` file to the project root
2. Set proper permissions: `chmod 600 prod-bastion-v6-data.pem`
3. Make sure the key file path in `.env` matches the actual file location

### Dependencies Added:

- `pg` - PostgreSQL client for Node.js
- `dotenv` - Environment variable loader
- `tunnel-ssh` - SSH tunnel for secure database connections

### Usage:

#### Basic Connection Test:
```bash
node index.js
```

#### Run Example Operations:
```bash
node example-usage.js
```

#### Import in Your Code:
```javascript
import { pool, executeQuery, testConnection } from './index.js';
import { userOperations, todoOperations } from './database.js';

// Test connection
await testConnection();

// Create a user
const user = await userOperations.create('<EMAIL>', 'hashedPassword');

// Create a todo
const todo = await todoOperations.create(user.id, 'My Todo', 'Description');
```

### Database Schema:

The application creates two main tables:

#### Users Table:
- `id` (SERIAL PRIMARY KEY)
- `email` (VARCHAR UNIQUE)
- `password_hash` (VARCHAR)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

#### Todos Table:
- `id` (SERIAL PRIMARY KEY)
- `user_id` (INTEGER, foreign key to users)
- `title` (VARCHAR)
- `description` (TEXT)
- `completed` (BOOLEAN)
- `priority` (VARCHAR)
- `due_date` (TIMESTAMP)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Available Operations:

#### User Operations:
- `userOperations.create(email, passwordHash)`
- `userOperations.findByEmail(email)`
- `userOperations.findById(id)`
- `userOperations.update(id, updates)`
- `userOperations.delete(id)`

#### Todo Operations:
- `todoOperations.create(userId, title, description, priority, dueDate)`
- `todoOperations.getByUserId(userId, completed)`
- `todoOperations.getById(id)`
- `todoOperations.update(id, updates)`
- `todoOperations.markCompleted(id)`
- `todoOperations.markIncomplete(id)`
- `todoOperations.delete(id)`
- `todoOperations.getByPriority(userId, priority)`
- `todoOperations.getOverdue(userId)`

### Connection Pool Features:

- Automatic connection pooling with up to 20 concurrent connections
- SSL support for AWS RDS connections
- SSH tunnel support for secure connections through bastion hosts
- Graceful shutdown handling
- Error handling and logging
- Connection timeout management
- Automatic fallback to direct connection if SSH tunnel fails

### SSH Tunnel Features:

- **Secure Connection**: Connects to your database through a bastion host
- **Automatic Setup**: Establishes SSH tunnel before database connection
- **Port Forwarding**: Maps local port to remote database port
- **Error Handling**: Falls back to direct connection if tunnel fails
- **Graceful Cleanup**: Properly closes tunnel on application shutdown

### Connection Flow:

1. **SSH Tunnel**: Application first attempts to create SSH tunnel to bastion host
2. **Port Forward**: Local port (5433) forwards to database port (5432) through tunnel
3. **Database Connection**: Connects to database via localhost:5433
4. **Fallback**: If SSH fails, attempts direct connection to database

### Notes:

- **SSH Key**: Make sure your SSH private key file is in the project root with proper permissions (600)
- **Network Access**: The bastion host (*************) must be accessible from your network
- **Security**: SSH tunnel provides encrypted connection to your production database
- **Performance**: SSH tunnel adds minimal latency while providing security
- All database operations use parameterized queries to prevent SQL injection
- The connection pool automatically handles connection management
- Tables are created automatically when you run the application