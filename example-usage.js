import { testConnection, closePool } from './index.js';
import { createTables, userOperations, todoOperations, getStats } from './database.js';

/**
 * Example usage of the database connection and operations
 */
async function exampleUsage() {
  try {
    console.log('🔄 Testing database connection...');
    
    // Test the connection first
    const isConnected = await testConnection();
    if (!isConnected) {
      console.log('❌ Cannot proceed without database connection');
      return;
    }

    console.log('\n📋 Creating tables...');
    await createTables();

    console.log('\n👤 Creating a test user...');
    const newUser = await userOperations.create(
      '<EMAIL>',
      'hashed_password_here'
    );
    console.log('Created user:', newUser);

    console.log('\n📝 Creating some todos...');
    const todo1 = await todoOperations.create(
      newUser.id,
      'Complete project documentation',
      'Write comprehensive documentation for the project',
      'high',
      new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Due in 7 days
    );

    const todo2 = await todoOperations.create(
      newUser.id,
      'Review code',
      'Review the latest pull requests',
      'medium'
    );

    const todo3 = await todoOperations.create(
      newUser.id,
      'Update dependencies',
      'Update all npm packages to latest versions',
      'low'
    );

    console.log('Created todos:', [todo1, todo2, todo3]);

    console.log('\n📋 Fetching all todos for user...');
    const allTodos = await todoOperations.getByUserId(newUser.id);
    console.log('All todos:', allTodos);

    console.log('\n✅ Marking first todo as completed...');
    const completedTodo = await todoOperations.markCompleted(todo1.id);
    console.log('Completed todo:', completedTodo);

    console.log('\n📊 Getting high priority todos...');
    const highPriorityTodos = await todoOperations.getByPriority(newUser.id, 'high');
    console.log('High priority todos:', highPriorityTodos);

    console.log('\n📈 Getting database statistics...');
    const stats = await getStats();
    console.log('Database stats:', stats);

    console.log('\n🔄 Updating a todo...');
    const updatedTodo = await todoOperations.update(todo2.id, {
      title: 'Review code and write tests',
      priority: 'high'
    });
    console.log('Updated todo:', updatedTodo);

    console.log('\n🗑️ Cleaning up test data...');
    await todoOperations.delete(todo1.id);
    await todoOperations.delete(todo2.id);
    await todoOperations.delete(todo3.id);
    await userOperations.delete(newUser.id);
    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.error('❌ Error in example usage:', error);
  } finally {
    // Always close the pool when done
    await closePool();
  }
}

// Run the example if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  exampleUsage().catch(console.error);
}

export { exampleUsage };
